{"VS Code Command": {"prefix": "vscode-command", "body": ["vscode.commands.registerCommand('${1:commandId}', (${2:args}) => {", "\t${3:// Command implementation}", "\tvscode.window.showInformationMessage('${4:Command executed!}');", "});"], "description": "Create a VS Code command"}, "VS Code Configuration": {"prefix": "vscode-config", "body": ["const config = vscode.workspace.getConfiguration('${1:configSection}');", "const ${2:setting} = config.get('${3:settingName}', ${4:defaultValue});"], "description": "Get VS Code configuration"}, "VS Code Status Bar Item": {"prefix": "vscode-statusbar", "body": ["const ${1:statusBarItem} = vscode.window.createStatusBarItem(vscode.StatusBarAlignment.${2:Left}, ${3:100});", "${1:statusBarItem}.text = '${4:Status Text}';", "${1:statusBarItem}.show();"], "description": "Create status bar item"}, "VS Code WebView": {"prefix": "vscode-webview", "body": ["const panel = vscode.window.createWebviewPanel(", "\t'${1:viewType}',", "\t'${2:Panel Title}',", "\tvscode.ViewColumn.${3:One},", "\t{", "\t\tenableScripts: true", "\t}", ");", "", "panel.webview.html = getWebviewContent();"], "description": "Create webview panel"}, "VS Code Tree Data Provider": {"prefix": "vscode-tree-provider", "body": ["class ${1:TreeDataProvider} implements vscode.TreeDataProvider<${2:TreeItem}> {", "\tprivate _onDidChangeTreeData: vscode.EventEmitter<${2:TreeItem} | undefined | null | void> = new vscode.EventEmitter<${2:TreeItem} | undefined | null | void>();", "\treadonly onDidChangeTreeData: vscode.Event<${2:TreeItem} | undefined | null | void> = this._onDidChangeTreeData.event;", "", "\tgetTreeItem(element: ${2:TreeItem}): vscode.TreeItem {", "\t\treturn element;", "\t}", "", "\tgetChildren(element?: ${2:TreeItem}): Thenable<${2:TreeItem}[]> {", "\t\t${3:// Return children}", "\t\treturn Promise.resolve([]);", "\t}", "", "\trefresh(): void {", "\t\tthis._onDidChangeTreeData.fire();", "\t}", "}"], "description": "Create tree data provider"}, "Arabic Comment Block": {"prefix": "ar-comment", "body": ["/**", " * ${1:وصف الدالة أو الكلاس}", " * @param {${2:type}} ${3:paramName} - ${4:وصف المعامل}", " * @returns {${5:type}} ${6:وصف القيمة المرجعة}", " */"], "description": "Arabic documentation comment"}, "Console Log Arabic": {"prefix": "clar", "body": ["console.log('${1:رسالة التصحيح}:', ${2:variable});"], "description": "<PERSON><PERSON>e log with Arabic message"}, "Error Handling Arabic": {"prefix": "try-ar", "body": ["try {", "\t${1:// الكود المراد تنفيذه}", "} catch (error) {", "\tconsole.error('${2:خطأ في العملية}:', error);", "\tvscode.window.showErrorMessage('${3:حدث خطأ أثناء العملية}');", "}"], "description": "Try-catch with Arabic messages"}}