{"version": "0.2.0", "configurations": [{"name": "Run Extension", "type": "extensionHost", "request": "launch", "args": ["--extensionDevelopmentPath=${workspaceFolder}"], "outFiles": ["${workspaceFolder}/out/**/*.js"], "preLaunchTask": "npm: compile"}, {"name": "Extension Tests", "type": "extensionHost", "request": "launch", "args": ["--extensionDevelopmentPath=${workspaceFolder}", "--extensionTestsPath=${workspaceFolder}/out/test/suite/index"], "outFiles": ["${workspaceFolder}/out/test/**/*.js"], "preLaunchTask": "npm: compile"}, {"name": "Run Extension (Development)", "type": "extensionHost", "request": "launch", "args": ["--extensionDevelopmentPath=${workspaceFolder}", "--disable-extensions"], "outFiles": ["${workspaceFolder}/out/**/*.js"], "preLaunchTask": "npm: compile", "env": {"NODE_ENV": "development"}}]}