{
    // إعدادات المحرر العامة
    "editor.tabSize": 4,
    "editor.insertSpaces": true,
    "editor.detectIndentation": true,
    "editor.trimAutoWhitespace": true,
    "editor.formatOnSave": true,
    "editor.formatOnPaste": true,
    "editor.codeActionsOnSave": {
        "source.fixAll": "explicit",
        "source.organizeImports": "explicit"
    },

    // إعدادات الملفات
    "files.autoSave": "afterDelay",
    "files.autoSaveDelay": 1000,
    "files.trimTrailingWhitespace": true,
    "files.insertFinalNewline": true,
    "files.trimFinalNewlines": true,
    "files.encoding": "utf8",

    // إعدادات JavaScript/TypeScript
    "javascript.preferences.includePackageJsonAutoImports": "auto",
    "typescript.preferences.includePackageJsonAutoImports": "auto",
    "javascript.suggest.autoImports": true,
    "typescript.suggest.autoImports": true,
    "javascript.updateImportsOnFileMove.enabled": "always",
    "typescript.updateImportsOnFileMove.enabled": "always",

    // إعدادات JSON
    "json.format.enable": true,
    "json.schemas": [
        {
            "fileMatch": ["package.json"],
            "url": "https://json.schemastore.org/package.json"
        },
        {
            "fileMatch": ["tsconfig.json", "tsconfig.*.json"],
            "url": "https://json.schemastore.org/tsconfig.json"
        }
    ],

    // إعدادات Git
    "git.enableSmartCommit": true,
    "git.confirmSync": false,
    "git.autofetch": true,

    // إعدادات البحث
    "search.exclude": {
        "**/node_modules": true,
        "**/out": true,
        "**/dist": true,
        "**/.vscode-test": true,
        "**/*.vsix": true
    },

    // إعدادات الملفات المستبعدة
    "files.exclude": {
        "**/.git": true,
        "**/.DS_Store": true,
        "**/node_modules": true,
        "**/out": true,
        "**/dist": true
    },

    // إعدادات المراقبة
    "files.watcherExclude": {
        "**/.git/objects/**": true,
        "**/node_modules/**": true,
        "**/out/**": true,
        "**/.vscode-test/**": true
    },

    // إعدادات التصحيح
    "debug.console.fontSize": 14,
    "debug.console.lineHeight": 22,

    // إعدادات الطرفية
    "terminal.integrated.fontSize": 14,
    "terminal.integrated.lineHeight": 1.2,

    // إعدادات خاصة بتطوير الإضافات
    "extensions.ignoreRecommendations": false,
    "workbench.startupEditor": "readme",

    // إعدادات اللغة العربية
    "workbench.editor.enablePreview": false,
    "editor.wordWrap": "on",
    "editor.rulers": [80, 120],

    // إعدادات التنسيق للملفات المختلفة
    "[javascript]": {
        "editor.defaultFormatter": "vscode.typescript-language-features",
        "editor.tabSize": 2
    },
    "[typescript]": {
        "editor.defaultFormatter": "vscode.typescript-language-features",
        "editor.tabSize": 2
    },
    "[json]": {
        "editor.defaultFormatter": "vscode.json-language-features",
        "editor.tabSize": 2
    },
    "[jsonc]": {
        "editor.defaultFormatter": "vscode.json-language-features",
        "editor.tabSize": 2
    },
    "[markdown]": {
        "editor.wordWrap": "on",
        "editor.quickSuggestions": {
            "comments": "off",
            "strings": "off",
            "other": "off"
        }
    }
}
