[
    // اختصارات مخصصة لتطوير الإضافات
    {
        "key": "ctrl+f5",
        "command": "workbench.action.debug.start",
        "when": "!inDebugMode"
    },
    {
        "key": "ctrl+shift+f5",
        "command": "workbench.action.debug.restart",
        "when": "inDebugMode"
    },
    {
        "key": "ctrl+shift+p",
        "command": "workbench.action.showCommands"
    },
    {
        "key": "ctrl+shift+e",
        "command": "workbench.view.explorer"
    },
    {
        "key": "ctrl+shift+g",
        "command": "workbench.view.scm"
    },
    {
        "key": "ctrl+shift+d",
        "command": "workbench.view.debug"
    },
    {
        "key": "ctrl+shift+x",
        "command": "workbench.view.extensions"
    },
    {
        "key": "ctrl+shift+y",
        "command": "workbench.debug.action.toggleRepl"
    },
    {
        "key": "ctrl+shift+u",
        "command": "workbench.action.output.toggleOutput"
    },
    {
        "key": "ctrl+shift+m",
        "command": "workbench.actions.view.problems"
    },
    {
        "key": "ctrl+shift+`",
        "command": "workbench.action.terminal.new"
    }
]
