{"version": "2.0.0", "tasks": [{"label": "npm: install", "type": "shell", "command": "npm", "args": ["install"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}, "problemMatcher": []}, {"label": "npm: compile", "type": "shell", "command": "npm", "args": ["run", "compile"], "group": "build", "presentation": {"echo": true, "reveal": "silent", "focus": false, "panel": "shared"}, "problemMatcher": "$tsc"}, {"label": "npm: watch", "type": "shell", "command": "npm", "args": ["run", "watch"], "group": "build", "presentation": {"echo": true, "reveal": "silent", "focus": false, "panel": "shared"}, "isBackground": true, "problemMatcher": "$tsc-watch"}, {"label": "npm: test", "type": "shell", "command": "npm", "args": ["test"], "group": "test", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}, "problemMatcher": []}, {"label": "npm: package", "type": "shell", "command": "npm", "args": ["run", "package"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}, "problemMatcher": []}, {"label": "vsce: package", "type": "shell", "command": "vsce", "args": ["package"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}, "problemMatcher": []}]}